# 505/512路由分发系统完整架构文档

## 📋 文档信息
- **文档版本**: v1.0
- **创建日期**: 2025-01-22
- **适用场景**: nginx多机器路由分发系统
- **技术栈**: Apache + nginx + 后端服务

## 🏗️ 系统架构图

```
客户端浏览器
    ↓ (HTTP请求)
Apache服务器 (IP:8089)
    ↓ (代理转发)
nginx主服务器 (端口9990) - 路由分发中心
    ├── /SDSJCPT/512/* → 机器B (端口9998)
    └── /SDSJCPT/* → 本机 (端口9998) [默认]
         ↓                    ↓
    本机后端服务         机器B后端服务
   (************:9999)  (************:9999)
```

## 🎯 系统设计目标

1. **路径分发**: 根据URL路径自动分发到不同机器
2. **透明代理**: 客户端无感知的多机器访问
3. **重定向处理**: 正确处理后端服务的重定向响应
4. **向后兼容**: 保持原有系统的访问方式不变

## 🔄 完整工作流程

### 场景1: 访问默认路径 (本机处理)

#### 1.1 静态资源请求流程
```
客户端请求: http://Apache的IP:8089/SDSJPCT/index.html
    ↓
Apache转发: http://nginx主服务器:9990/SDSJCPT/index.html
    ↓
nginx主服务器匹配: location /SDSJCPT/
    ↓
转发到本机: http://127.0.0.1:9998/SDSJCPT/index.html
    ↓
本机nginx处理: 返回静态文件 /home/<USER>/sds/RKDT/nginx/dist/index.html
    ↓
响应返回: 客户端收到页面
```

#### 1.2 API请求流程
```
客户端AJAX: http://Apache的IP:8089/SDSJPCT/everisk/api/getUserInfo
    ↓
Apache转发: http://nginx主服务器:9990/SDSJCPT/everisk/api/getUserInfo
    ↓
nginx主服务器匹配: location /SDSJCPT/
    ↓
转发到本机: http://127.0.0.1:9998/SDSJCPT/everisk/api/getUserInfo
    ↓
本机nginx匹配: location /SDSJCPT/everisk/api/
    ↓
代理到后端: http://************:9999/SDSJCPT/everisk/api/getUserInfo
    ↓
后端处理并返回JSON数据
    ↓
响应链路返回: 客户端收到API响应
```

#### 1.3 重定向处理流程
```
客户端请求: http://Apache的IP:8089/SDSJPCT/login
    ↓
经过Apache和nginx主服务器转发到本机后端
    ↓
后端返回重定向: Location: http://************:9999/SDSJCPT/dashboard
    ↓
本机nginx处理: 转换为 Location: /SDSJCPT/dashboard
    ↓
nginx主服务器处理: proxy_redirect http://127.0.0.1:9998/SDSJCPT/ /SDSJCPT/
    转换为: Location: /SDSJCPT/dashboard
    ↓
Apache处理: 转换为 Location: http://Apache的IP:8089/SDSJPCT/dashboard
    ↓
客户端收到正确重定向: 浏览器跳转到正确地址
```

### 场景2: 访问512路径 (机器B处理)

#### 2.1 静态资源请求流程
```
客户端请求: http://Apache的IP:8089/SDSJPCT/512/index.html
    ↓
Apache转发: http://nginx主服务器:9990/SDSJCPT/512/index.html
    ↓
nginx主服务器匹配: location ~ ^/SDSJCPT/512/
    ↓
转发到机器B: http://机器B的IP:9998/SDSJCPT/512/index.html
    ↓
机器B nginx处理: 匹配 location ~ ^/SDSJCPT/512/
    重写路径: rewrite ^/SDSJCPT/512/(.*)$ /$1 break;
    实际访问: /index.html
    ↓
返回静态文件: /home/<USER>/sds/RKDT/nginx/dist/index.html (机器B上的)
    ↓
响应返回: 客户端收到机器B的页面
```

#### 2.2 API请求流程
```
客户端AJAX: http://Apache的IP:8089/SDSJPCT/512/everisk/api/getUserInfo
    ↓
Apache转发: http://nginx主服务器:9990/SDSJCPT/512/everisk/api/getUserInfo
    ↓
nginx主服务器匹配: location ~ ^/SDSJCPT/512/
    ↓
转发到机器B: http://机器B的IP:9998/SDSJCPT/512/everisk/api/getUserInfo
    ↓
机器B nginx匹配: location /SDSJCPT/512/everisk/api/
    重写路径: rewrite ^/SDSJCPT/512/(.*)$ /SDSJCPT/$1 break;
    ↓
代理到后端: http://************:9999/SDSJCPT/everisk/api/getUserInfo
    ↓
后端处理并返回JSON数据
    ↓
响应链路返回: 客户端收到API响应
```

#### 2.3 重定向处理流程
```
客户端请求: http://Apache的IP:8089/SDSJPCT/512/login
    ↓
经过Apache和nginx主服务器转发到机器B
    ↓
机器B转发到后端，后端返回重定向: Location: http://************:9999/SDSJCPT/dashboard
    ↓
机器B nginx处理: 转换为 Location: /SDSJCPT/dashboard
    ↓
nginx主服务器处理: proxy_redirect http://机器B的IP:9998/SDSJCPT/ /SDSJCPT/512/
    转换为: Location: /SDSJCPT/512/dashboard
    ↓
Apache处理: 转换为 Location: http://Apache的IP:8089/SDSJPCT/512/dashboard
    ↓
客户端收到正确重定向: 浏览器跳转到512环境的正确地址
```

## ⚙️ 关键配置解析

### nginx主服务器配置 (nginx.conf)
```nginx
# 机器B的upstream配置
upstream machine_b_server {
    server 机器B的IP:9998;
}

server {
    listen 9990;  # Apache转发到这个端口

    # 512路径处理 - 优先级高，先匹配
    location ~ ^/SDSJCPT/512/ {
        proxy_pass http://machine_b_server;
        proxy_set_header Host $http_host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        # 关键：处理机器B返回的重定向，自动加上512前缀
        proxy_redirect http://机器B的IP:9998/SDSJCPT/ /SDSJCPT/512/;
    }

    # 默认路径处理 - 兜底匹配
    location /SDSJCPT/ {
        proxy_pass http://127.0.0.1:9998;
        proxy_set_header Host $http_host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        # 关键：处理本机返回的重定向
        proxy_redirect http://127.0.0.1:9998/SDSJCPT/ /SDSJCPT/;
    }
}
```

### 本机nginx配置 (nginx_front_original.conf)
```nginx
upstream everisk_server {
    ip_hash;
    server ************:9999;  # 后端服务地址
}

server {
    listen 9998;  # 接收主nginx的转发
    root /home/<USER>/sds/RKDT/nginx/dist;  # 静态文件目录

    # 静态资源处理
    location /SDSJCPT/everisk/static/ {
        root /home/<USER>/sds/RKDT/nginx/dist;
    }

    # API代理
    location /SDSJCPT/everisk/api/ {
        proxy_pass http://everisk_server;
        proxy_redirect off;
        proxy_set_header Host $http_host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        # ... 其他代理配置
    }

    # SPA路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }
}
```

### 机器B nginx配置 (nginx_front_machine_b.conf)
```nginx
upstream everisk_server_b {
    ip_hash;
    server ************:9999;  # 机器B的后端服务地址
}

server {
    listen 9998;  # 接收主nginx的转发
    root /home/<USER>/sds/RKDT/nginx/dist;  # 机器B的静态文件目录

    # 512路径的静态资源处理
    location /SDSJCPT/512/everisk/static/ {
        root /home/<USER>/sds/RKDT/nginx/dist;
        # 去掉512前缀匹配实际文件
        rewrite ^/SDSJCPT/512/(.*)$ /$1 break;
    }

    # 512路径的API处理
    location /SDSJCPT/512/everisk/api/ {
        proxy_pass http://everisk_server_b;
        # 去掉512前缀转发给后端
        rewrite ^/SDSJCPT/512/(.*)$ /SDSJCPT/$1 break;
        proxy_redirect off;
        proxy_set_header Host $http_host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        # ... 其他代理配置
    }

    # 512路径的SPA路由支持
    location ~ ^/SDSJCPT/512/ {
        try_files $uri $uri/ /index.html;
    }
}
```

## 🎯 关键技术点

### 1. 路径匹配优先级
- `location ~ ^/SDSJCPT/512/` (正则匹配) 优先级高于 `location /SDSJCPT/` (前缀匹配)
- 确保512路径先被处理，避免被默认路径拦截

### 2. 重定向处理链
- **后端服务** → **nginx代理** → **主nginx** → **Apache** → **客户端**
- 每一层都正确处理Location头的转换，确保客户端收到正确的重定向地址

### 3. 路径重写技术
- 机器B收到带512前缀的请求，需要去掉前缀才能匹配实际文件和API
- 使用 `rewrite ^/SDSJCPT/512/(.*)$ /$1 break;` 进行路径转换

### 4. proxy_redirect 配置
- **本机**: `proxy_redirect http://127.0.0.1:9998/SDSJCPT/ /SDSJCPT/;`
- **机器B**: `proxy_redirect http://机器B的IP:9998/SDSJCPT/ /SDSJCPT/512/;`
- 确保重定向时自动添加或保持正确的路径前缀

## 🔧 前端配置要求

### 本机前端配置 (无需修改)
- 保持原有的API路径: `/SDSJCPT/everisk/api/`
- 保持原有的静态资源路径: `/SDSJCPT/everisk/static/`
- 保持原有的路由配置
- 无需任何代码修改

### 机器B前端配置 (需要512前缀)

#### Vue.js 项目配置
```javascript
// vue.config.js
module.exports = {
  publicPath: '/SDSJCPT/512/',

  devServer: {
    proxy: {
      '/SDSJCPT/512/everisk/api': {
        target: 'http://localhost:9998',
        changeOrigin: true,
        pathRewrite: {
          '^/SDSJCPT/512': '/SDSJCPT'
        }
      }
    }
  }
}

// router/index.js
import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory('/SDSJCPT/512/'),
  routes: [/* 路由配置 */]
})

// API配置
import axios from 'axios'
const api = axios.create({
  baseURL: '/SDSJCPT/512/everisk/api'
})
```

#### React 项目配置
```json
// package.json
{
  "homepage": "/SDSJCPT/512/",
  "scripts": {
    "build:512": "env-cmd -f .env.512 react-scripts build"
  }
}
```

```javascript
// React Router配置
import { BrowserRouter } from 'react-router-dom'

function App() {
  return (
    <BrowserRouter basename="/SDSJCPT/512">
      {/* 路由组件 */}
    </BrowserRouter>
  )
}

// API配置
import axios from 'axios'
const api = axios.create({
  baseURL: '/SDSJCPT/512/everisk/api'
})
```

## 🚀 部署步骤

### 1. 环境准备
- [ ] 确认Apache服务器正常运行 (端口8089)
- [ ] 确认后端服务正常运行 (************:9999)
- [ ] 准备nginx主服务器 (端口9990)
- [ ] 准备机器B (端口9998)

### 2. 配置文件部署

#### 2.1 修改nginx主服务器配置
```bash
# 编辑 nginx.conf
# 将 "机器B的IP" 替换为实际IP地址
upstream machine_b_server {
    server *************:9998;  # 替换为实际IP
}
```

#### 2.2 部署本机nginx配置
```bash
# 使用 nginx_front_original.conf
# 无需修改，保持原有配置
cp nginx_front_original.conf /etc/nginx/conf.d/nginx_front.conf
```

#### 2.3 部署机器B nginx配置
```bash
# 将 nginx_front_machine_b.conf 复制到机器B
scp nginx_front_machine_b.conf user@机器B的IP:/etc/nginx/conf.d/
# 在机器B上修改配置中的路径和后端地址
```

### 3. 前端应用部署

#### 3.1 本机前端部署
```bash
# 保持原有构建方式
npm run build
cp -r dist/* /home/<USER>/sds/RKDT/nginx/dist/
```

#### 3.2 机器B前端部署
```bash
# 使用512前缀配置构建
export VUE_APP_BASE_PATH=/SDSJCPT/512/
npm run build
scp -r dist/* user@机器B的IP:/home/<USER>/sds/RKDT/nginx/dist/
```

### 4. 服务启动顺序
1. 启动后端服务 (************:9999)
2. 启动本机nginx (端口9998)
3. 启动机器B nginx (端口9998)
4. 启动nginx主服务器 (端口9990)
5. 确认Apache配置正确 (端口8089)

## ✅ 测试验证

### 1. 基础连通性测试
```bash
# 测试本机nginx
curl http://127.0.0.1:9998/SDSJCPT/everisk/api/health

# 测试机器B nginx
curl http://机器B的IP:9998/SDSJCPT/512/everisk/api/health

# 测试主nginx路由
curl http://主服务器IP:9990/SDSJCPT/everisk/api/health
curl http://主服务器IP:9990/SDSJCPT/512/everisk/api/health
```

### 2. 完整链路测试
```bash
# 通过Apache访问默认环境
curl http://Apache服务器IP:8089/SDSJPCT/everisk/api/health

# 通过Apache访问512环境
curl http://Apache服务器IP:8089/SDSJPCT/512/everisk/api/health
```

### 3. 前端页面测试
- 访问默认环境: `http://Apache服务器IP:8089/SDSJPCT/`
- 访问512环境: `http://Apache服务器IP:8089/SDSJPCT/512/`
- 检查浏览器开发者工具中的网络请求
- 验证API调用路径正确
- 测试页面跳转和重定向功能

## 🔍 故障排查指南

### 1. 常见问题及解决方案

#### 问题1: 512路径访问404
**症状**: 访问 `/SDSJCPT/512/` 返回404错误
**排查步骤**:
```bash
# 检查nginx主服务器配置
nginx -t
# 检查机器B连通性
telnet 机器B的IP 9998
# 检查路径匹配
curl -v http://主服务器IP:9990/SDSJCPT/512/
```
**解决方案**: 确认机器B IP地址配置正确，nginx配置语法正确

#### 问题2: 重定向地址错误
**症状**: 重定向后地址变成内网IP或丢失前缀
**排查步骤**:
```bash
# 检查重定向配置
grep proxy_redirect /etc/nginx/nginx.conf
# 测试重定向响应
curl -I http://Apache服务器IP:8089/SDSJPCT/512/login
```
**解决方案**: 检查proxy_redirect配置是否正确

#### 问题3: 静态资源加载失败
**症状**: 页面加载但CSS/JS文件404
**排查步骤**:
- 检查前端构建配置中的publicPath
- 确认静态文件部署路径正确
- 检查nginx静态文件配置

### 2. 日志查看
```bash
# nginx主服务器日志
tail -f /var/log/nginx/error.log
tail -f /var/log/nginx/access.log

# 机器B nginx日志
ssh user@机器B的IP "tail -f /var/log/nginx/error.log"

# Apache日志
tail -f /var/log/apache2/error.log
```

### 3. 配置验证命令
```bash
# 验证nginx配置语法
nginx -t

# 重新加载nginx配置
nginx -s reload

# 检查端口监听状态
netstat -tlnp | grep :9990
netstat -tlnp | grep :9998
```

## 📊 性能优化建议

### 1. 负载均衡优化
```nginx
# 如果有多台机器B，可以配置负载均衡
upstream machine_b_cluster {
    ip_hash;
    server 机器B1的IP:9998;
    server 机器B2的IP:9998;
    server 机器B3的IP:9998;
}
```

### 2. 缓存配置
```nginx
# 静态资源缓存
location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

### 3. 连接优化
```nginx
# 保持连接
proxy_http_version 1.1;
proxy_set_header Connection "";

# 连接池
upstream backend {
    server ************:9999;
    keepalive 32;
}
```

## 📝 维护清单

### 日常维护
- [ ] 定期检查nginx日志，关注错误信息
- [ ] 监控各服务器的资源使用情况
- [ ] 定期测试重定向功能是否正常
- [ ] 备份重要配置文件

### 扩展计划
- [ ] 支持更多路径前缀 (如513、514等)
- [ ] 添加健康检查机制
- [ ] 实现自动故障转移
- [ ] 添加访问日志分析

## 🎉 系统优势

1. **高可用性**: 多机器分布式部署，单点故障不影响整体服务
2. **易扩展性**: 可以轻松添加更多机器和路径前缀
3. **透明性**: 客户端无感知的多机器访问
4. **兼容性**: 保持原有系统访问方式不变
5. **灵活性**: 支持不同环境的独立部署和配置

## 📞 技术支持

如遇到问题，请按以下顺序排查：
1. 检查配置文件语法
2. 查看相关日志文件
3. 验证网络连通性
4. 测试各个环节的功能
5. 参考本文档的故障排查指南

---

**文档结束**

*本文档详细描述了505/512路由分发系统的完整架构和实现方案，包含了从设计理念到具体实现的所有细节。*
