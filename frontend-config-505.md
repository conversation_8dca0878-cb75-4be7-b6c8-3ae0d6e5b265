# 前端配置 - 505版本

## Vue.js 项目配置

### 1. vue.config.js
```javascript
module.exports = {
  // 设置公共路径为505前缀
  publicPath: process.env.NODE_ENV === 'production' ? '/SDSJCPT/505/' : '/',
  
  // 开发服务器配置
  devServer: {
    port: 8080,
    proxy: {
      '/SDSJCPT/505/everisk/api': {
        target: 'http://localhost:9998',
        changeOrigin: true,
        pathRewrite: {
          '^/SDSJCPT/505': '/SDSJCPT'
        }
      }
    }
  },
  
  // 构建配置
  configureWebpack: {
    output: {
      // 确保资源路径正确
      publicPath: '/SDSJCPT/505/'
    }
  }
}
```

### 2. router/index.js (Vue Router配置)
```javascript
import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  // 设置base路径
  history: createWebHistory('/SDSJCPT/505/'),
  routes: [
    // 你的路由配置
  ]
})

export default router
```

### 3. API配置 (utils/api.js)
```javascript
import axios from 'axios'

// 创建axios实例
const api = axios.create({
  baseURL: '/SDSJCPT/505/everisk/api',  // 带505前缀的API基础路径
  timeout: 10000
})

export default api
```

## React 项目配置

### 1. package.json
```json
{
  "name": "your-app",
  "version": "1.0.0",
  "homepage": "/SDSJCPT/505/",
  "scripts": {
    "build": "react-scripts build",
    "start": "react-scripts start"
  }
}
```

### 2. React Router配置
```javascript
import { BrowserRouter } from 'react-router-dom'

function App() {
  return (
    <BrowserRouter basename="/SDSJCPT/505">
      {/* 你的路由组件 */}
    </BrowserRouter>
  )
}
```

### 3. API配置
```javascript
import axios from 'axios'

const api = axios.create({
  baseURL: '/SDSJCPT/505/everisk/api'
})

export default api
```

## 通用JavaScript配置

### 动态路径检测方案
```javascript
// 获取当前环境的基础路径
function getBasePath() {
  const path = window.location.pathname
  if (path.includes('/505/')) {
    return '/SDSJCPT/505'
  } else if (path.includes('/512/')) {
    return '/SDSJCPT/512'
  }
  return '/SDSJCPT'  // 默认路径
}

// API调用工具
const apiCall = {
  get: (endpoint) => {
    const basePath = getBasePath()
    return fetch(`${basePath}/everisk/api${endpoint}`)
  },
  post: (endpoint, data) => {
    const basePath = getBasePath()
    return fetch(`${basePath}/everisk/api${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    })
  }
}

// 使用示例
apiCall.get('/user/info').then(response => response.json())
```

## 构建和部署

### 构建命令
```bash
# Vue项目
npm run build

# React项目  
npm run build

# 构建后的文件应该部署到nginx的dist目录下
# 确保index.html中的资源路径都包含/SDSJCPT/505/前缀
```

### 验证配置
构建完成后，检查生成的index.html文件，确保：
1. 所有CSS和JS文件路径都包含 `/SDSJCPT/505/` 前缀
2. 图片等静态资源路径正确
3. API调用路径包含正确的前缀
