server {
        listen 9990;

        # Make site accessible from http://localhost/
        # server_name localhost;

        add_header X-Frame-Options SAMEORIGIN;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection 1;
        add_header Cache-Control 'no-cache, no-store, must-revalidate';
        add_header Pragma no-cache;
        # 传输最大文件体积
        client_max_body_size 1000M;

        # 日志是否输出
        # access_log  /data/bangcle_front/nginx/access.log;
         access_log  /tmp/access.log;
        # error_log  /data/bangcle_front/nginx/error.log;

        default_type 'text/html';
        charset utf-8;


        # 捕获接口请求代理到webservice
        location / {
                proxy_pass  http://*************:9998;
                proxy_set_header x-real-ip $remote_addr;
                proxy_set_header Host $http_host;
                deny    ***********;
                allow   *************;
                deny    all;

        }
}

