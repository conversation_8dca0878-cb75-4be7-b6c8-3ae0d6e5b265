upstream stress_servers{
    server *************:8101;
    server *************:8101;
    server *************:8101;
    server *************:8101;
    server ************:8101;
}

server {
    listen 6278;

    client_max_body_size 10M;

    location /NCCB/CCBCommonTXRoute {
        proxy_pass http://stress_servers;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /NCCB/TransactionRoute_1 {
        proxy_pass http://stress_servers;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /NCCB/CCBQueryRoute {
        proxy_pass http://stress_servers;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /NCCB/CCBQueryRoute_1 {
        proxy_pass http://stress_servers;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

}




