
upstream server_list{
  	ip_hash;
	server ************:9998;
}

server {
        listen 8103;

        add_header X-XSS-Protection 1;
        add_header Cache-Control 'no-cache, no-store, must-revalidate';
        add_header Pragma no-cache;
        # 传输最大文件体积
        client_max_body_size 1000M;

        # 日志是否输出
        # access_log  /data/bangcle_front/nginx/access.log;
        # error_log  /data/bangcle_front/nginx/error.log;

        default_type 'text/html';
        charset utf-8;

	location /SDSJCPT/everisk/api/v4/kibana/ {
		#要求登陆认证
        	auth_basic "kibana login auth";
         	#密码文件路径
        	auth_basic_user_file /home/<USER>/sds/RKDT/nginx/conf/passwd/.htpasswd;
         	# 转发到 kibana
	       	proxy_pass http://127.0.0.1:5602;
		#去除前缀
		rewrite /SDSJCPT/everisk/api/v4/kibana/(.*)$ /$1 break;
        	proxy_set_header Host $host;
         	proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }

	# 捕获接口请求代理到webservice
        location /SDSJCPT/ {
                proxy_pass  http://server_list;
                proxy_set_header   Host             $http_host;
                proxy_set_header   X-Real-IP        $remote_addr;
                proxy_set_header   X-Forwarded-For  $proxy_add_x_forwarded_for;
	}
}




