worker_processes  4;

#error_log  logs/error.log;
#error_log  logs/error.log  notice;
#error_log  logs/error.log  info;

#pid        logs/nginx.pid;


events {
    worker_connections  40980;
}


http {
    include       mime.types;
    default_type  application/octet-stream;

    #log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
    #                  '$status $body_bytes_sent "$http_referer" '
    #                  '"$http_user_agent" "$http_x_forwarded_for"';

    #access_log  logs/access.log  main;

    sendfile        on;
    #tcp_nopush     on;

    #keepalive_timeout  0;
    keepalive_timeout  65;

    #gzip  on;
    
    server_names_hash_bucket_size 64;
    client_max_body_size 50m;
    
#    server {
#        listen       80;
#        server_name  localhost;
#
#        #charset koi8-r;
#
#        #access_log  logs/host.access.log  main;
#
#        location / {
#            root   html;
#            index  index.html index.htm;
#        }
#
#        #error_page  404              /404.html;
#
#        # redirect server error pages to the static page /50x.html
#        #
#        error_page   500 502 503 504  /50x.html;
#        location = /50x.html {
#            root   html;
#        }
#
#        # proxy the PHP scripts to Apache listening on 127.0.0.1:80
#        #
#        #location ~ \.php$ {
#        #    proxy_pass   http://127.0.0.1;
#        #}
#
#        # pass the PHP scripts to FastCGI server listening on 127.0.0.1:9000
#        #
#        #location ~ \.php$ {
#        #    root           html;
#        #    fastcgi_pass   127.0.0.1:9000;
#        #    fastcgi_index  index.php;
#        #    fastcgi_param  SCRIPT_FILENAME  /scripts$fastcgi_script_name;
#        #    include        fastcgi_params;
#        #}
#
#        # deny access to .htaccess files, if Apache's document root
#        # concurs with nginx's one
#        #
#        #location ~ /\.ht {
#        #    deny  all;
#        #}
#    }
#
#
#    # another virtual host using mix of IP-, name-, and port-based configuration
    #
    #server {
    #    listen       8000;
    #    listen       somename:8080;
    #    server_name  somename  alias  another.alias;

    #    location / {
    #        root   html;
    #        index  index.html index.htm;
    #    }
    #}


    # HTTPS server
    #
    #server {
    #    listen       443 ssl;
    #    server_name  localhost;

    #    ssl_certificate      cert.pem;
    #    ssl_certificate_key  cert.key;

    #    ssl_session_cache    shared:SSL:1m;
    #    ssl_session_timeout  5m;

    #    ssl_ciphers  HIGH:!aNULL:!MD5;
    #    ssl_prefer_server_ciphers  on;

    #    location / {
    #        root   html;
    #        index  index.html index.htm;
    #    }
    #}
    # 512路由分发服务器
    upstream machine_b_server {
        server 机器B的IP:9998;  # 请替换为机器B的实际IP地址
    }

    server {
        listen 9990;  # Apache转发到这个端口

        # 处理512路径 - 转发到机器B
        location ~ ^/SDSJCPT/512/ {
            proxy_pass http://machine_b_server;
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            # 处理512路径的重定向：机器B返回的重定向地址转换为相对路径
            proxy_redirect http://机器B的IP:9998/SDSJCPT/ /SDSJCPT/512/;
        }

        # 默认处理 - 转发到本机9998端口
        location /SDSJCPT/ {
            proxy_pass http://127.0.0.1:9998;
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            # 处理默认路径的重定向：本机返回的重定向地址转换为相对路径
            proxy_redirect http://127.0.0.1:9998/SDSJCPT/ /SDSJCPT/;
        }
    }

include conf.d/*.conf;
}
