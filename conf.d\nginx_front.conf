upstream everisk_server {
	ip_hash;
	server ************:9999;
}

server {
        listen 9998;

        # 前端资源存放位置
        root /home/<USER>/sds/RKDT/nginx/dist;

        index index.html index.htm;

        add_header X-Frame-Options SAMEORIGIN;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection 1;
        add_header Cache-Control 'no-cache, no-store, must-revalidate';
        add_header Pragma no-cache;
        # 传输最大文件体积
        client_max_body_size 1000M;

        # 日志是否输出
        # access_log  /data/bangcle_front/nginx/access.log;
        # error_log  /data/bangcle_front/nginx/error.log;

        default_type 'text/html';
        charset utf-8;

        # 默认路由到资源路径
        location / {
                try_files $uri $uri/ /index.html;
#                deny    ***********;
#                allow   ************;
#                deny    all;
        }

        # 默认路由到资源路径
        location / {
                try_files $uri $uri/ /index.html;
#                deny    ***********;
#                allow   ************;
#                deny    all;
        }
        # 505路径的静态资源处理
        location /SDSJCPT/505/everisk/static/ {
                root /home/<USER>/sds/RKDT/nginx/dist;
                # 重写路径，去掉505前缀来匹配实际文件
                rewrite ^/SDSJCPT/505/(.*)$ /$1 break;
        }

        # 505路径的API请求处理
        location /SDSJCPT/505/everisk/api/ {
                proxy_pass  http://everisk_server;
                # 重写路径，去掉505前缀
                rewrite ^/SDSJCPT/505/(.*)$ /SDSJCPT/$1 break;
                #Proxy Settings
                proxy_redirect     off;
                proxy_set_header   Host             $http_host;
                proxy_set_header   X-Real-IP        $remote_addr;
                proxy_set_header   X-Forwarded-For  $proxy_add_x_forwarded_for;
                proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
                proxy_max_temp_file_size 0;
                proxy_connect_timeout      90;
                proxy_send_timeout         90;
                proxy_read_timeout         90;
                proxy_buffer_size          4k;
                proxy_buffers              4 32k;
                proxy_busy_buffers_size    64k;
                proxy_temp_file_write_size 64k;
        }

        # 505路径的uass请求处理
        location /SDSJCPT/505/uass/ {
                proxy_pass  http://everisk_server;
                # 重写路径，去掉505前缀
                rewrite ^/SDSJCPT/505/(.*)$ /SDSJCPT/$1 break;
                #Proxy Settings
                proxy_redirect     off;
                proxy_set_header   Host             $http_host;
                proxy_set_header   X-Real-IP        $remote_addr;
                proxy_set_header   X-Forwarded-For  $proxy_add_x_forwarded_for;
                proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
                proxy_max_temp_file_size 0;
                proxy_connect_timeout      90;
                proxy_send_timeout         90;
                proxy_read_timeout         90;
                proxy_buffer_size          4k;
                proxy_buffers              4 32k;
                proxy_busy_buffers_size    64k;
                proxy_temp_file_write_size 64k;
        }

        # 505路径的uass-core请求处理
        location /SDSJCPT/505/uass-core/ {
                proxy_pass  http://everisk_server;
                # 重写路径，去掉505前缀
                rewrite ^/SDSJCPT/505/(.*)$ /SDSJCPT/$1 break;
                #Proxy Settings
                proxy_redirect     off;
                proxy_set_header   Host             $http_host;
                proxy_set_header   X-Real-IP        $remote_addr;
                proxy_set_header   X-Forwarded-for  $proxy_add_x_forwarded_for;
                proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
                proxy_max_temp_file_size 0;
                proxy_connect_timeout      90;
                proxy_send_timeout         90;
                proxy_read_timeout         90;
                proxy_buffer_size          4k;
                proxy_buffers              4 32k;
                proxy_busy_buffers_size    64k;
                proxy_temp_file_write_size 64k;
        }

        # 原有的资源路径地址（兼容性保留）
        location /SDSJCPT/everisk/static/ {
                root /home/<USER>/sds/RKDT/nginx/dist;
        }

        # 处理丢失前缀的API请求 - 根据Referer判断应该转发到哪里
        location /SDSJCPT/everisk/api/ {
                # 检查Referer头，如果来自505页面，重定向到505路径
                if ($http_referer ~ "^https?://[^/]+/SDSJCPT/505/") {
                    rewrite ^/SDSJCPT/everisk/api/(.*)$ /SDSJCPT/505/everisk/api/$1 redirect;
                }
                # 检查Referer头，如果来自512页面，重定向到512路径
                if ($http_referer ~ "^https?://[^/]+/SDSJCPT/512/") {
                    rewrite ^/SDSJCPT/everisk/api/(.*)$ /SDSJCPT/512/everisk/api/$1 redirect;
                }

                # 默认处理（兼容性保留）
                proxy_pass  http://everisk_server;
                #Proxy Settings
                proxy_redirect     off;
                proxy_set_header   Host             $http_host;
                proxy_set_header   X-Real-IP        $remote_addr;
                proxy_set_header   X-Forwarded-For  $proxy_add_x_forwarded_for;
                proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
                proxy_max_temp_file_size 0;
                proxy_connect_timeout      90;
                proxy_send_timeout         90;
                proxy_read_timeout         90;
                proxy_buffer_size          4k;
                proxy_buffers              4 32k;
                proxy_busy_buffers_size    64k;
                proxy_temp_file_write_size 64k;
                #proxy_set_header Refere "http://************:8103/SDSJCPT/everisk/index.html";

                # proxy_pass_header   Server;
                # proxy_set_header   X-Scheme         $scheme;
        }

        location /SDSJCPT/uass/ {
                # 检查Referer头，如果来自505页面，重定向到505路径
                if ($http_referer ~ "^https?://[^/]+/SDSJCPT/505/") {
                    rewrite ^/SDSJCPT/uass/(.*)$ /SDSJCPT/505/uass/$1 redirect;
                }
                # 检查Referer头，如果来自512页面，重定向到512路径
                if ($http_referer ~ "^https?://[^/]+/SDSJCPT/512/") {
                    rewrite ^/SDSJCPT/uass/(.*)$ /SDSJCPT/512/uass/$1 redirect;
                }

                proxy_pass  http://everisk_server;
                #Proxy Settings
                proxy_redirect     off;
                proxy_set_header   Host             $http_host;
                proxy_set_header   X-Real-IP        $remote_addr;
                proxy_set_header   X-Forwarded-For  $proxy_add_x_forwarded_for;
                proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
                proxy_max_temp_file_size 0;
                proxy_connect_timeout      90;
                proxy_send_timeout         90;
                proxy_read_timeout         90;
                proxy_buffer_size          4k;
                proxy_buffers              4 32k;
                proxy_busy_buffers_size    64k;
                proxy_temp_file_write_size 64k;
        }
        location /SDSJCPT/uass-core/ {
                # 检查Referer头，如果来自505页面，重定向到505路径
                if ($http_referer ~ "^https?://[^/]+/SDSJCPT/505/") {
                    rewrite ^/SDSJCPT/uass-core/(.*)$ /SDSJCPT/505/uass-core/$1 redirect;
                }
                # 检查Referer头，如果来自512页面，重定向到512路径
                if ($http_referer ~ "^https?://[^/]+/SDSJCPT/512/") {
                    rewrite ^/SDSJCPT/uass-core/(.*)$ /SDSJCPT/512/uass-core/$1 redirect;
                }

                proxy_pass  http://everisk_server;
                #Proxy Settings
                proxy_redirect     off;
                proxy_set_header   Host             $http_host;
                proxy_set_header   X-Real-IP        $remote_addr;
                proxy_set_header   X-Forwarded-For  $proxy_add_x_forwarded_for;
                proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
                proxy_max_temp_file_size 0;
                proxy_connect_timeout      90;
                proxy_send_timeout         90;
                proxy_read_timeout         90;
                proxy_buffer_size          4k;
                proxy_buffers              4 32k;
                proxy_busy_buffers_size    64k;
                proxy_temp_file_write_size 64k;
        }


}
