# 前端配置 - 512版本

## Vue.js 项目配置

### 1. vue.config.js
```javascript
module.exports = {
  // 设置公共路径为512前缀
  publicPath: process.env.NODE_ENV === 'production' ? '/SDSJCPT/512/' : '/',
  
  // 开发服务器配置
  devServer: {
    port: 8080,
    proxy: {
      '/SDSJCPT/512/everisk/api': {
        target: 'http://localhost:9998',
        changeOrigin: true,
        pathRewrite: {
          '^/SDSJCPT/512': '/SDSJCPT'
        }
      }
    }
  },
  
  // 构建配置
  configureWebpack: {
    output: {
      // 确保资源路径正确
      publicPath: '/SDSJCPT/512/'
    }
  }
}
```

### 2. router/index.js (Vue Router配置)
```javascript
import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  // 设置base路径
  history: createWebHistory('/SDSJCPT/512/'),
  routes: [
    // 你的路由配置
  ]
})

export default router
```

### 3. API配置 (utils/api.js)
```javascript
import axios from 'axios'

// 创建axios实例
const api = axios.create({
  baseURL: '/SDSJCPT/512/everisk/api',  // 带512前缀的API基础路径
  timeout: 10000
})

export default api
```

## React 项目配置

### 1. package.json
```json
{
  "name": "your-app",
  "version": "1.0.0",
  "homepage": "/SDSJCPT/512/",
  "scripts": {
    "build": "react-scripts build",
    "start": "react-scripts start"
  }
}
```

### 2. React Router配置
```javascript
import { BrowserRouter } from 'react-router-dom'

function App() {
  return (
    <BrowserRouter basename="/SDSJCPT/512">
      {/* 你的路由组件 */}
    </BrowserRouter>
  )
}
```

### 3. API配置
```javascript
import axios from 'axios'

const api = axios.create({
  baseURL: '/SDSJCPT/512/everisk/api'
})

export default api
```

## 环境变量配置方案（推荐）

### 1. 创建环境变量文件

**.env.505** (505环境)
```
REACT_APP_BASE_PATH=/SDSJCPT/505
REACT_APP_API_BASE_URL=/SDSJCPT/505/everisk/api
VUE_APP_BASE_PATH=/SDSJCPT/505
VUE_APP_API_BASE_URL=/SDSJCPT/505/everisk/api
```

**.env.512** (512环境)
```
REACT_APP_BASE_PATH=/SDSJCPT/512
REACT_APP_API_BASE_URL=/SDSJCPT/512/everisk/api
VUE_APP_BASE_PATH=/SDSJCPT/512
VUE_APP_API_BASE_URL=/SDSJCPT/512/everisk/api
```

### 2. 使用环境变量

**Vue项目 (vue.config.js)**
```javascript
module.exports = {
  publicPath: process.env.VUE_APP_BASE_PATH || '/',
  
  configureWebpack: {
    output: {
      publicPath: process.env.VUE_APP_BASE_PATH || '/'
    }
  }
}
```

**React项目 (package.json)**
```json
{
  "homepage": "%REACT_APP_BASE_PATH%",
  "scripts": {
    "build:505": "env-cmd -f .env.505 react-scripts build",
    "build:512": "env-cmd -f .env.512 react-scripts build"
  }
}
```

### 3. API配置使用环境变量
```javascript
import axios from 'axios'

const api = axios.create({
  baseURL: process.env.REACT_APP_API_BASE_URL || process.env.VUE_APP_API_BASE_URL
})

export default api
```

## 构建脚本

### package.json 构建脚本
```json
{
  "scripts": {
    "build:505": "env-cmd -f .env.505 npm run build",
    "build:512": "env-cmd -f .env.512 npm run build",
    "deploy:505": "npm run build:505 && cp -r dist/* /path/to/machine-a/nginx/dist/",
    "deploy:512": "npm run build:512 && cp -r dist/* /path/to/machine-b/nginx/dist/"
  },
  "devDependencies": {
    "env-cmd": "^10.1.0"
  }
}
```

## 部署说明

1. **机器A部署505版本**：
   ```bash
   npm run build:505
   # 将dist目录内容部署到机器A的nginx目录
   ```

2. **机器B部署512版本**：
   ```bash
   npm run build:512
   # 将dist目录内容部署到机器B的nginx目录
   ```

这样可以用同一套代码构建出不同前缀的版本，部署到不同的机器上。
