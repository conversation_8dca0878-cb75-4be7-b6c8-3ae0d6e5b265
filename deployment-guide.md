# 505/512 路由分发部署指南

## 🎯 架构概览

```
Apache (8089) 
    ↓
nginx主服务器 (9990) - 路由分发
    ├── /SDSJCPT/505/* → 机器A (9998)
    └── /SDSJCPT/512/* → 机器B (9998)
```

## 📋 部署步骤

### 1. 修改nginx.conf配置

在nginx.conf中已添加路由分发配置，需要修改机器B的IP地址：

```nginx
upstream machine_b_server {
    server *************:9998;  # 请替换为机器B的实际IP地址
}
```

### 2. 机器A配置 (保持原有nginx_front.conf不变)

机器A使用原有的 `nginx_front_original.conf` 配置，监听9998端口。

### 3. 机器B配置

将 `nginx_front_machine_b.conf` 部署到机器B，并根据实际情况修改：

```nginx
# 修改后端服务器地址
upstream everisk_server_b {
    ip_hash;
    server ************:9999;  # 机器B的后端服务器地址
}

# 修改静态文件路径
root /home/<USER>/sds/RKDT/nginx/dist;  # 机器B的实际路径
```

### 4. Apache配置 (保持不变)

Apache配置保持原样：
```apache
ProxyPass /SDSJPCT/ http://nginx主服务器IP:9990/SDSJCPT/
ProxyPassReverse /SDSJPCT/ http://nginx主服务器IP:9990/SDSJCPT/
```

## 🔧 前端配置

### 方案A: 分别构建 (推荐)

为505和512分别构建不同版本的前端：

**505版本构建**：
```bash
# 设置环境变量
export VUE_APP_BASE_PATH=/SDSJCPT/505/
export VUE_APP_API_BASE_URL=/SDSJCPT/505/everisk/api

# 构建
npm run build

# 部署到机器A
cp -r dist/* /path/to/machine-a/nginx/dist/
```

**512版本构建**：
```bash
# 设置环境变量  
export VUE_APP_BASE_PATH=/SDSJCPT/512/
export VUE_APP_API_BASE_URL=/SDSJCPT/512/everisk/api

# 构建
npm run build

# 部署到机器B
cp -r dist/* /path/to/machine-b/nginx/dist/
```

### 方案B: 动态检测路径

使用同一套前端代码，通过JavaScript动态检测路径：

```javascript
// 在前端代码中添加
function getBasePath() {
  const path = window.location.pathname;
  if (path.includes('/505/')) {
    return '/SDSJCPT/505';
  } else if (path.includes('/512/')) {
    return '/SDSJCPT/512';
  }
  return '/SDSJCPT';
}

// API调用时使用
const basePath = getBasePath();
const apiUrl = `${basePath}/everisk/api/your-endpoint`;
```

## 🚀 启动顺序

1. **启动后端服务** (************:9999)
2. **启动机器A的nginx** (端口9998)
3. **启动机器B的nginx** (端口9998)  
4. **启动主nginx服务器** (端口9990)
5. **确认Apache配置** (端口8089)

## ✅ 测试验证

### 1. 直接访问测试
```bash
# 测试机器A
curl http://机器A的IP:9998/SDSJCPT/everisk/api/test

# 测试机器B  
curl http://机器B的IP:9998/SDSJCPT/everisk/api/test

# 测试主nginx路由
curl http://主服务器IP:9990/SDSJCPT/505/everisk/api/test
curl http://主服务器IP:9990/SDSJCPT/512/everisk/api/test
```

### 2. 完整链路测试
```bash
# 通过Apache访问505
curl http://Apache服务器IP:8089/SDSJPCT/505/everisk/api/test

# 通过Apache访问512
curl http://Apache服务器IP:8089/SDSJPCT/512/everisk/api/test
```

### 3. 前端页面测试
- 访问: `http://Apache服务器IP:8089/SDSJPCT/505/index.html`
- 访问: `http://Apache服务器IP:8089/SDSJPCT/512/index.html`
- 检查浏览器开发者工具中的网络请求，确认API调用路径正确

## 🔍 故障排查

### 1. 检查nginx配置语法
```bash
nginx -t
```

### 2. 查看nginx日志
```bash
tail -f /var/log/nginx/error.log
tail -f /var/log/nginx/access.log
```

### 3. 检查端口监听
```bash
netstat -tlnp | grep :9998
netstat -tlnp | grep :9990
```

### 4. 测试upstream连接
```bash
# 测试机器B连接
telnet 机器B的IP 9998
```

## 📝 配置文件清单

- ✅ `nginx.conf` - 主配置文件，包含路由分发逻辑
- ✅ `conf.d/nginx_front_original.conf` - 机器A的配置文件
- ✅ `conf.d/nginx_front_machine_b.conf` - 机器B的配置文件  
- ✅ `frontend-config-505.md` - 505版本前端配置说明
- ✅ `frontend-config-512.md` - 512版本前端配置说明

## 🎉 完成后的访问方式

- **505环境**: `http://Apache服务器IP:8089/SDSJPCT/505/` → 机器A
- **512环境**: `http://Apache服务器IP:8089/SDSJPCT/512/` → 机器B
- **默认环境**: `http://Apache服务器IP:8089/SDSJPCT/` → 机器A (兼容性)
