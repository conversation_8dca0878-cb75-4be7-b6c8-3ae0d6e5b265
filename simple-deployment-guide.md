# 简化版路由分发部署指南

## 🎯 架构概览

```
Apache (8089) 
    ↓
nginx主服务器 (9990) - 路由分发
    ├── /SDSJCPT/512/* → 机器B (9998)
    └── /SDSJCPT/* → 本机 (9998) [默认]
```

## 📋 部署步骤

### 1. 修改nginx.conf配置

在nginx.conf中已添加简化的路由分发配置，只需要修改机器B的IP地址：

```nginx
upstream machine_b_server {
    server *************:9998;  # 请替换为机器B的实际IP地址
}
```

### 2. 本机配置

本机使用原有的 `nginx_front_original.conf` 配置，监听9998端口，无需修改。

### 3. 机器B配置

将 `nginx_front_machine_b.conf` 部署到机器B，并根据实际情况修改：

```nginx
# 修改后端服务器地址
upstream everisk_server_b {
    ip_hash;
    server ************:9999;  # 机器B的后端服务器地址
}

# 修改静态文件路径
root /home/<USER>/sds/RKDT/nginx/dist;  # 机器B的实际路径
```

### 4. Apache配置 (保持不变)

Apache配置保持原样：
```apache
ProxyPass /SDSJPCT/ http://nginx主服务器IP:9990/SDSJCPT/
ProxyPassReverse /SDSJPCT/ http://nginx主服务器IP:9990/SDSJCPT/
```

## 🔧 前端配置

### 本机前端 (无需修改)

本机的前端保持原有配置，不需要任何修改：
- 使用原有的API路径: `/SDSJCPT/everisk/api/`
- 使用原有的静态资源路径: `/SDSJCPT/everisk/static/`
- 保持原有的路由配置

### 机器B前端 (需要512前缀)

机器B的前端需要配置512前缀，参考 `frontend-config-512.md`：

**Vue.js 配置**:
```javascript
// vue.config.js
module.exports = {
  publicPath: '/SDSJCPT/512/',
  
  devServer: {
    proxy: {
      '/SDSJCPT/512/everisk/api': {
        target: 'http://localhost:9998',
        changeOrigin: true,
        pathRewrite: {
          '^/SDSJCPT/512': '/SDSJCPT'
        }
      }
    }
  }
}

// API配置
const api = axios.create({
  baseURL: '/SDSJCPT/512/everisk/api'
})
```

## 🚀 启动顺序

1. **启动后端服务** (************:9999)
2. **启动本机nginx** (端口9998) - 使用nginx_front_original.conf
3. **启动机器B的nginx** (端口9998) - 使用nginx_front_machine_b.conf
4. **启动主nginx服务器** (端口9990) - 使用修改后的nginx.conf
5. **确认Apache配置** (端口8089)

## ✅ 测试验证

### 1. 直接访问测试
```bash
# 测试本机
curl http://127.0.0.1:9998/SDSJCPT/everisk/api/test

# 测试机器B  
curl http://机器B的IP:9998/SDSJCPT/everisk/api/test

# 测试主nginx路由
curl http://主服务器IP:9990/SDSJCPT/everisk/api/test  # 应该转发到本机
curl http://主服务器IP:9990/SDSJCPT/512/everisk/api/test  # 应该转发到机器B
```

### 2. 完整链路测试
```bash
# 通过Apache访问本机 (默认)
curl http://Apache服务器IP:8089/SDSJPCT/everisk/api/test

# 通过Apache访问机器B
curl http://Apache服务器IP:8089/SDSJPCT/512/everisk/api/test
```

### 3. 前端页面测试
- **本机访问**: `http://Apache服务器IP:8089/SDSJPCT/index.html`
- **机器B访问**: `http://Apache服务器IP:8089/SDSJPCT/512/index.html`

## 🎉 完成后的访问方式

- **默认环境** (本机): `http://Apache服务器IP:8089/SDSJPCT/` → 本机9998端口
- **512环境** (机器B): `http://Apache服务器IP:8089/SDSJPCT/512/` → 机器B9998端口

## 📝 需要的配置文件

- ✅ `nginx.conf` - 主配置文件，包含简化的路由分发逻辑
- ✅ `conf.d/nginx_front_original.conf` - 本机的配置文件 (无需修改)
- ✅ `conf.d/nginx_front_machine_b.conf` - 机器B的配置文件
- ✅ `frontend-config-512.md` - 机器B的前端配置说明

## 🔧 关键修改点

1. **本机**: 无需任何修改，保持原有配置
2. **机器B**: 需要配置512前缀的前端应用
3. **nginx.conf**: 简化了路由逻辑，默认转发到本机

这样的配置更简单，本机保持原有的工作方式，只有机器B需要特殊配置！
