upstream everisk_server_b {
	ip_hash;
	server ************:9999;  # 机器B的后端服务器地址，请根据实际情况修改
}

server {
        listen 9998;

        # 前端资源存放位置
        root /home/<USER>/sds/RKDT/nginx/dist;  # 请根据机器B的实际路径修改

        index index.html index.htm;

        add_header X-Frame-Options SAMEORIGIN;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection 1;
        add_header Cache-Control 'no-cache, no-store, must-revalidate';
        add_header Pragma no-cache;
        # 传输最大文件体积
        client_max_body_size 1000M;

        # 日志是否输出
        # access_log  /data/bangcle_front/nginx/access.log;
        # error_log  /data/bangcle_front/nginx/error.log;

        default_type 'text/html';
        charset utf-8;

        # 处理512路径的请求（机器B本地处理）
        location ~ ^/SDSJCPT/512/ {
                # 直接处理，不重写URL，保持/512前缀
                try_files $uri $uri/ /index.html;
        }

        # 512路径的静态资源处理
        location /SDSJCPT/512/everisk/static/ {
                root /home/<USER>/sds/RKDT/nginx/dist;  # 请根据机器B的实际路径修改
                # 重写路径，去掉512前缀来匹配实际文件
                rewrite ^/SDSJCPT/512/(.*)$ /$1 break;
        }

        # 512路径的API请求处理
        location /SDSJCPT/512/everisk/api/ {
                proxy_pass  http://everisk_server_b;
                # 重写路径，去掉512前缀
                rewrite ^/SDSJCPT/512/(.*)$ /SDSJCPT/$1 break;
                #Proxy Settings
                proxy_redirect     off;
                proxy_set_header   Host             $http_host;
                proxy_set_header   X-Real-IP        $remote_addr;
                proxy_set_header   X-Forwarded-For  $proxy_add_x_forwarded_for;
                proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
                proxy_max_temp_file_size 0;
                proxy_connect_timeout      90;
                proxy_send_timeout         90;
                proxy_read_timeout         90;
                proxy_buffer_size          4k;
                proxy_buffers              4 32k;
                proxy_busy_buffers_size    64k;
                proxy_temp_file_write_size 64k;
        }

        # 512路径的uass请求处理
        location /SDSJCPT/512/uass/ {
                proxy_pass  http://everisk_server_b;
                # 重写路径，去掉512前缀
                rewrite ^/SDSJCPT/512/(.*)$ /SDSJCPT/$1 break;
                #Proxy Settings
                proxy_redirect     off;
                proxy_set_header   Host             $http_host;
                proxy_set_header   X-Real-IP        $remote_addr;
                proxy_set_header   X-Forwarded-For  $proxy_add_x_forwarded_for;
                proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
                proxy_max_temp_file_size 0;
                proxy_connect_timeout      90;
                proxy_send_timeout         90;
                proxy_read_timeout         90;
                proxy_buffer_size          4k;
                proxy_buffers              4 32k;
                proxy_busy_buffers_size    64k;
                proxy_temp_file_write_size 64k;
        }

        # 512路径的uass-core请求处理
        location /SDSJCPT/512/uass-core/ {
                proxy_pass  http://everisk_server_b;
                # 重写路径，去掉512前缀
                rewrite ^/SDSJCPT/512/(.*)$ /SDSJCPT/$1 break;
                #Proxy Settings
                proxy_redirect     off;
                proxy_set_header   Host             $http_host;
                proxy_set_header   X-Real-IP        $remote_addr;
                proxy_set_header   X-Forwarded-For  $proxy_add_x_forwarded_for;
                proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
                proxy_max_temp_file_size 0;
                proxy_connect_timeout      90;
                proxy_send_timeout         90;
                proxy_read_timeout         90;
                proxy_buffer_size          4k;
                proxy_buffers              4 32k;
                proxy_busy_buffers_size    64k;
                proxy_temp_file_write_size 64k;
        }

        # 默认路由到资源路径
        location / {
                try_files $uri $uri/ /index.html;
        }
}
