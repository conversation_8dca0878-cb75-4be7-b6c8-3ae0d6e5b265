upstream everisk_server {
	ip_hash;
	server ************:9999;
}

server {
        listen 9998;

        # 前端资源存放位置
        root /home/<USER>/sds/RKDT/nginx/dist;

        index index.html index.htm;

        add_header X-Frame-Options SAMEORIGIN;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection 1;
        add_header Cache-Control 'no-cache, no-store, must-revalidate';
        add_header Pragma no-cache;
        # 传输最大文件体积
        client_max_body_size 1000M;

        # 日志是否输出
        # access_log  /data/bangcle_front/nginx/access.log;
        # error_log  /data/bangcle_front/nginx/error.log;

        default_type 'text/html';
        charset utf-8;

        # 默认路由到资源路径
        location / {
                try_files $uri $uri/ /index.html;
#                deny    ***********;
#                allow   ************;
#                deny    all;
        }
        # 资源路径地址
        location /SDSJCPT/everisk/static/ {
                root /home/<USER>/sds/RKDT/nginx/dist;
        }

        # 捕获接口请求代理到webservice
        location /SDSJCPT/everisk/api/ {
                proxy_pass  http://everisk_server;
                #Proxy Settings
                proxy_redirect     off;
                proxy_set_header   Host             $http_host;
                proxy_set_header   X-Real-IP        $remote_addr;
                proxy_set_header   X-Forwarded-For  $proxy_add_x_forwarded_for;
                proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
                proxy_max_temp_file_size 0;
                proxy_connect_timeout      90;
                proxy_send_timeout         90;
                proxy_read_timeout         90;
                proxy_buffer_size          4k;
                proxy_buffers              4 32k;
                proxy_busy_buffers_size    64k;
                proxy_temp_file_write_size 64k;
                #proxy_set_header Refere "http://************:8103/SDSJCPT/everisk/index.html";

                # proxy_pass_header   Server;
                # proxy_set_header   X-Scheme         $scheme;
        }

        location /SDSJCPT/uass/ {
                proxy_pass  http://everisk_server;
                #Proxy Settings
                proxy_redirect     off;
                proxy_set_header   Host             $http_host;
                proxy_set_header   X-Real-IP        $remote_addr;
                proxy_set_header   X-Forwarded-For  $proxy_add_x_forwarded_for;
                proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
                proxy_max_temp_file_size 0;
                proxy_connect_timeout      90;
                proxy_send_timeout         90;
                proxy_read_timeout         90;
                proxy_buffer_size          4k;
                proxy_buffers              4 32k;
                proxy_busy_buffers_size    64k;
                proxy_temp_file_write_size 64k;
        }
        location /SDSJCPT/uass-core/ {
                proxy_pass  http://everisk_server;
                #Proxy Settings
                proxy_redirect     off;
                proxy_set_header   Host             $http_host;
                proxy_set_header   X-Real-IP        $remote_addr;
                proxy_set_header   X-Forwarded-For  $proxy_add_x_forwarded_for;
                proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
                proxy_max_temp_file_size 0;
                proxy_connect_timeout      90;
                proxy_send_timeout         90;
                proxy_read_timeout         90;
                proxy_buffer_size          4k;
                proxy_buffers              4 32k;
                proxy_busy_buffers_size    64k;
                proxy_temp_file_write_size 64k;
        }


}
